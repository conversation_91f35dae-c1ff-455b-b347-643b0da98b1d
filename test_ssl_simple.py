#!/usr/bin/env python3
"""
Simple SSL test without <PERSON><PERSON><PERSON> to verify the SSL configuration works
"""
import os
import requests
import urllib3

# Set environment variable
os.environ['TPA_SSL_VERIFY'] = 'false'

def test_ssl_bypass():
    """Test SSL bypass directly with requests"""
    print("🧪 Testing SSL Bypass with Direct Requests")
    print("="*50)
    
    # TPA API details
    base_url = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
    endpoint = "/api/GetToken"
    full_url = f"{base_url}{endpoint}"
    
    print(f"Target URL: {full_url}")
    
    # Test data
    payload = {
        "USERNAME": "BVTPA",
        "PASSWORD": "*d!n^+Cb@1",
        "SOCIAL_ID": "U3ef2199803607a9ec643f2461fd2f039",
        "CHANNEL_ID": "2006769099",
        "CHANNEL": "LINE"
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Disable SSL warnings
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    print(f"\n1. Testing with SSL verification ENABLED (should fail):")
    try:
        response = requests.post(full_url, data=payload, headers=headers, verify=True, timeout=30)
        print(f"   ❌ Unexpected success: {response.status_code}")
    except requests.exceptions.SSLError as e:
        print(f"   ✅ Expected SSL error: {str(e)[:100]}...")
    except Exception as e:
        print(f"   ❓ Other error: {str(e)[:100]}...")
    
    print(f"\n2. Testing with SSL verification DISABLED (should work or give different error):")
    try:
        response = requests.post(full_url, data=payload, headers=headers, verify=False, timeout=30)
        print(f"   ✅ Success! Status: {response.status_code}")
        print(f"   Response: {response.text[:100]}...")
        return True
    except requests.exceptions.SSLError as e:
        print(f"   ❌ Still getting SSL error: {str(e)[:100]}...")
        return False
    except Exception as e:
        print(f"   ✅ Different error (SSL bypass working): {str(e)[:100]}...")
        return True

def test_session_ssl():
    """Test SSL bypass with requests session (like TPA service)"""
    print(f"\n3. Testing with requests Session (like TPA service):")
    
    session = requests.Session()
    session.verify = False  # Disable SSL verification
    
    base_url = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
    endpoint = "/api/GetToken"
    full_url = f"{base_url}{endpoint}"
    
    payload = {
        "USERNAME": "BVTPA",
        "PASSWORD": "*d!n^+Cb@1",
        "SOCIAL_ID": "U3ef2199803607a9ec643f2461fd2f039",
        "CHANNEL_ID": "2006769099",
        "CHANNEL": "LINE"
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        response = session.post(full_url, data=payload, headers=headers, timeout=30)
        print(f"   ✅ Success! Status: {response.status_code}")
        print(f"   Response: {response.text[:100]}...")
        return True
    except requests.exceptions.SSLError as e:
        print(f"   ❌ Still getting SSL error: {str(e)[:100]}...")
        return False
    except Exception as e:
        print(f"   ✅ Different error (SSL bypass working): {str(e)[:100]}...")
        return True

if __name__ == "__main__":
    print("🔧 Simple SSL Configuration Test\n")
    
    success1 = test_ssl_bypass()
    success2 = test_session_ssl()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 SSL bypass is working correctly!")
        print("The issue may be in how the TPA service is configured.")
    elif success1 or success2:
        print("✅ SSL bypass is partially working.")
        print("There may be configuration issues in the TPA service.")
    else:
        print("❌ SSL bypass is not working.")
        print("There may be network or certificate issues.")
    
    print(f"\nEnvironment check:")
    print(f"  TPA_SSL_VERIFY: {os.environ.get('TPA_SSL_VERIFY', 'not set')}")
    print(f"  Python version: {os.sys.version}")
    print(f"  Requests version: {requests.__version__}")
