#!/usr/bin/env python3
"""
Direct test of TPA service to verify SSL configuration and endpoint URLs
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')
django.setup()

from customer._services.tpa_service import TPAApiService

def test_tpa_direct():
    """Test TPA service directly"""
    print("🧪 Testing TPA Service Direct Connection")
    print("="*50)
    
    # Ensure SSL verification is disabled
    os.environ['TPA_SSL_VERIFY'] = 'false'
    print(f"TPA_SSL_VERIFY: {os.environ.get('TPA_SSL_VERIFY')}")
    
    # Create TPA service instance
    tpa_service = TPAApiService()
    print(f"SSL verification enabled: {tpa_service.verify_ssl}")
    print(f"Session verify setting: {tpa_service.session.verify}")
    print(f"Base URL: {tpa_service.BASE_URL}")
    
    # Test parameters (these are test values from the workflow config)
    test_params = {
        'social_id': 'U3ef2199803607a9ec643f2461fd2f039',
        'channel_id': '2006769099',
        'channel': 'LINE'
    }
    
    print(f"\nTest parameters:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")
    
    print(f"\n1. Testing get_bearer_token...")
    try:
        token = tpa_service.get_bearer_token(
            test_params['social_id'],
            test_params['channel_id'],
            test_params['channel']
        )
        print(f"✅ Success! Token received: {token[:20]}..." if len(token) > 20 else f"✅ Success! Token: {token}")
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error: {error_msg}")
        
        if "SSL" in error_msg or "certificate" in error_msg.lower():
            print("   This is still an SSL certificate error.")
            print("   The SSL configuration may not be working correctly.")
            return False
        elif "401" in error_msg or "authentication" in error_msg.lower():
            print("   This appears to be an authentication error.")
            print("   ✅ SSL configuration is working - the error is not SSL-related.")
            return True
        elif "404" in error_msg or "not found" in error_msg.lower():
            print("   This appears to be a URL/endpoint error.")
            print("   ✅ SSL configuration is working - the error is not SSL-related.")
            return True
        else:
            print("   This appears to be a different type of error.")
            print("   ✅ SSL configuration may be working correctly.")
            return True

def test_dynamic_request():
    """Test the dynamic request method used by the workflow"""
    print(f"\n2. Testing make_dynamic_request (used by workflow)...")
    
    os.environ['TPA_SSL_VERIFY'] = 'false'
    tpa_service = TPAApiService()
    
    # Test the exact same call that the workflow makes
    endpoint = "/api/GetToken"
    method = "POST"
    payload = {
        "USERNAME": "BVTPA",
        "PASSWORD": "*d!n^+Cb@1",
        "SOCIAL_ID": "U3ef2199803607a9ec643f2461fd2f039",
        "CHANNEL_ID": "2006769099",
        "CHANNEL": "LINE"
    }
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    print(f"   Endpoint: {endpoint}")
    print(f"   Method: {method}")
    print(f"   Full URL will be: {tpa_service.BASE_URL}{endpoint}")
    
    try:
        result = tpa_service.make_dynamic_request(endpoint, method, payload, headers)
        print(f"✅ Success! Result: {result[:50]}..." if len(str(result)) > 50 else f"✅ Success! Result: {result}")
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error: {error_msg}")
        
        if "SSL" in error_msg or "certificate" in error_msg.lower():
            print("   This is still an SSL certificate error.")
            print("   The SSL configuration is not working correctly.")
            return False
        else:
            print("   This appears to be a non-SSL error.")
            print("   ✅ SSL configuration is working correctly.")
            return True

if __name__ == "__main__":
    print("🔧 Direct TPA Service Test\n")
    
    success1 = test_tpa_direct()
    success2 = test_dynamic_request()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 All tests passed!")
        print("SSL configuration is working correctly.")
        print("The TPA service should now work with the policy workflow.")
    elif success1 or success2:
        print("✅ SSL configuration appears to be working.")
        print("Some tests passed, indicating SSL errors are resolved.")
    else:
        print("❌ SSL configuration is not working correctly.")
        print("Please check the environment variables and restart the application.")
    
    print(f"\nCurrent environment:")
    print(f"  TPA_SSL_VERIFY: {os.environ.get('TPA_SSL_VERIFY', 'not set')}")
    print(f"  Django DEBUG: {os.environ.get('DEBUG', 'not set')}")
