"""
Django management command to test TPA SSL configuration
"""
import os
from django.core.management.base import BaseCommand
from customer._services.tpa_service import TPAApiService


class Command(BaseCommand):
    help = 'Test TPA SSL configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--ssl-verify',
            choices=['true', 'false'],
            default='false',
            help='Set SSL verification (default: false)'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing TPA SSL Configuration')
        )
        self.stdout.write('=' * 50)

        # Set environment variable based on option
        ssl_verify = options['ssl_verify']
        os.environ['TPA_SSL_VERIFY'] = ssl_verify
        
        self.stdout.write(f"Setting TPA_SSL_VERIFY={ssl_verify}")
        
        # Create TPA service instance
        tpa_service = TPAApiService()
        
        self.stdout.write(f"TPA Service Configuration:")
        self.stdout.write(f"  verify_ssl: {tpa_service.verify_ssl}")
        self.stdout.write(f"  session.verify: {tpa_service.session.verify}")
        self.stdout.write(f"  base_url: {tpa_service.BASE_URL}")
        
        # Test parameters
        test_params = {
            'social_id': 'U3ef2199803607a9ec643f2461fd2f039',
            'channel_id': '2006769099',
            'channel': 'LINE'
        }
        
        self.stdout.write(f"\nTesting get_bearer_token with test parameters...")
        
        try:
            token = tpa_service.get_bearer_token(
                test_params['social_id'],
                test_params['channel_id'],
                test_params['channel']
            )
            self.stdout.write(
                self.style.SUCCESS(f"✅ Success! Token received: {token[:20]}...")
            )
            
        except Exception as e:
            error_msg = str(e)
            
            if "SSL" in error_msg or "certificate" in error_msg.lower():
                self.stdout.write(
                    self.style.ERROR(f"❌ SSL Error: {error_msg}")
                )
                self.stdout.write(
                    self.style.WARNING("SSL configuration is not working correctly.")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"⚠️  Non-SSL Error: {error_msg}")
                )
                self.stdout.write(
                    self.style.SUCCESS("✅ SSL configuration appears to be working.")
                )
        
        # Test dynamic request method
        self.stdout.write(f"\nTesting make_dynamic_request method...")
        
        try:
            result = tpa_service.make_dynamic_request(
                "/api/GetToken",
                "POST",
                {
                    "USERNAME": "BVTPA",
                    "PASSWORD": "*d!n^+Cb@1",
                    "SOCIAL_ID": test_params['social_id'],
                    "CHANNEL_ID": test_params['channel_id'],
                    "CHANNEL": test_params['channel']
                },
                {"Content-Type": "application/x-www-form-urlencoded"}
            )
            self.stdout.write(
                self.style.SUCCESS(f"✅ Success! Result: {str(result)[:50]}...")
            )
            
        except Exception as e:
            error_msg = str(e)
            
            if "SSL" in error_msg or "certificate" in error_msg.lower():
                self.stdout.write(
                    self.style.ERROR(f"❌ SSL Error: {error_msg}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"⚠️  Non-SSL Error: {error_msg}")
                )
                self.stdout.write(
                    self.style.SUCCESS("✅ SSL configuration appears to be working.")
                )
        
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write(
            self.style.SUCCESS('TPA SSL configuration test completed.')
        )
