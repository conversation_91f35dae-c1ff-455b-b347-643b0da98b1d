#!/usr/bin/env python3
"""
Test environment variable loading
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Check environment before Django setup
print("🔍 Environment Check Before Django Setup")
print("="*50)
print(f"TPA_SSL_VERIFY (os.environ): {os.environ.get('TPA_SSL_VERIFY', 'NOT SET')}")
print(f"DEBUG (os.environ): {os.environ.get('DEBUG', 'NOT SET')}")
print(f"DEBUG_MODE (os.environ): {os.environ.get('DEBUG_MODE', 'NOT SET')}")

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')
django.setup()

print(f"\n🔍 Environment Check After Django Setup")
print("="*50)
print(f"TPA_SSL_VERIFY (os.environ): {os.environ.get('TPA_SSL_VERIFY', 'NOT SET')}")

# Test TPA service initialization
print(f"\n🔍 TPA Service Initialization Test")
print("="*50)

from customer._services.tpa_service import TPAApiService

# Create TPA service and check its configuration
tpa_service = TPAApiService()
print(f"TPA Service verify_ssl: {tpa_service.verify_ssl}")
print(f"TPA Service session.verify: {tpa_service.session.verify}")

# Test environment variable changes
print(f"\n🔍 Dynamic Environment Variable Test")
print("="*50)

# Change environment variable and create new service
os.environ['TPA_SSL_VERIFY'] = 'true'
tpa_service_true = TPAApiService()
print(f"After setting TPA_SSL_VERIFY=true:")
print(f"  TPA Service verify_ssl: {tpa_service_true.verify_ssl}")
print(f"  TPA Service session.verify: {tpa_service_true.session.verify}")

os.environ['TPA_SSL_VERIFY'] = 'false'
tpa_service_false = TPAApiService()
print(f"After setting TPA_SSL_VERIFY=false:")
print(f"  TPA Service verify_ssl: {tpa_service_false.verify_ssl}")
print(f"  TPA Service session.verify: {tpa_service_false.session.verify}")

# Test different values
print(f"\n🔍 Environment Variable Value Test")
print("="*50)

test_values = ['true', 'True', 'TRUE', 'false', 'False', 'FALSE', 'yes', 'no', '1', '0', '']

for value in test_values:
    os.environ['TPA_SSL_VERIFY'] = value
    result = os.getenv('TPA_SSL_VERIFY', 'true').lower() == 'true'
    print(f"  TPA_SSL_VERIFY='{value}' -> verify_ssl={result}")

# Reset to false for testing
os.environ['TPA_SSL_VERIFY'] = 'false'
print(f"\n✅ Environment variable test completed.")
print(f"Final TPA_SSL_VERIFY setting: {os.environ.get('TPA_SSL_VERIFY')}")
